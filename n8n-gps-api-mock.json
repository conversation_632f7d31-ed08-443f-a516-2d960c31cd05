{"name": "GPS API Mock - Untuk Cronjob", "nodes": [{"parameters": {"httpMethod": "POST", "path": "user/login", "responseMode": "responseNode", "options": {}}, "id": "gps-login-webhook", "name": "GPS Login", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "gps-login"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.username }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.body.password }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-gps-login", "name": "Validate GPS Login", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"message\": \"Login successful\",\n  \"data\": {\n    \"token\": \"gps_mock_token_{{ $now.format('x') }}\",\n    \"expires_in\": 3600,\n    \"user_id\": \"{{ $json.body.username }}\",\n    \"user_type\": \"admin\",\n    \"permissions\": [\"device_read\", \"device_control\"]\n  }\n}"}, "id": "gps-login-success", "name": "GPS Login Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 240]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 401,\n  \"message\": \"Invalid credentials\",\n  \"data\": null\n}", "responseCode": 401}, "id": "gps-login-error", "name": "GPS Login Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 360]}, {"parameters": {"httpMethod": "POST", "path": "device/list_all", "responseMode": "responseNode", "options": {}}, "id": "gps-devices-webhook", "name": "GPS Device List All", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500], "webhookId": "gps-devices"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.headers.authorization }}", "rightValue": "Bearer", "operator": {"type": "string", "operation": "contains"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-gps-auth", "name": "Validate GPS Auth", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"message\": \"Devices retrieved successfully\",\n  \"data\": [\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-6) }}\",\n      \"imei\": \"86{{ $now.format('x').slice(-13) }}\",\n      \"name\": \"Mock GPS Device 1 - Active\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 80) + 20 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": true,\n      \"lastUpdate\": \"{{ $now.toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 40) + 60 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }},\n      \"status\": \"ACTIVE\",\n      \"vehicleType\": \"Truck\",\n      \"plateNumber\": \"B {{ Math.floor(Math.random() * 9000) + 1000 }} ABC\"\n    },\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-5) }}1\",\n      \"imei\": \"86{{ $now.format('x').slice(-12) }}1\",\n      \"name\": \"Mock GPS Device 2 - Moving\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 60) + 40 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": true,\n      \"lastUpdate\": \"{{ $now.minus({ minutes: Math.floor(Math.random() * 5) }).toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 50) + 30 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }},\n      \"status\": \"MOVING\",\n      \"vehicleType\": \"Container\",\n      \"plateNumber\": \"B {{ Math.floor(Math.random() * 9000) + 1000 }} XYZ\"\n    },\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-5) }}2\",\n      \"imei\": \"86{{ $now.format('x').slice(-12) }}2\",\n      \"name\": \"Mock GPS Device 3 - Idle\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": 0,\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": {{ Math.random() > 0.2 }},\n      \"lastUpdate\": \"{{ $now.minus({ minutes: Math.floor(Math.random() * 15) }).toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 60) + 40 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }},\n      \"status\": \"IDLE\",\n      \"vehicleType\": \"Van\",\n      \"plateNumber\": \"B {{ Math.floor(Math.random() * 9000) + 1000 }} DEF\"\n    },\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-5) }}3\",\n      \"imei\": \"86{{ $now.format('x').slice(-12) }}3\",\n      \"name\": \"Mock GPS Device 4 - Offline\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": 0,\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": false,\n      \"lastUpdate\": \"{{ $now.minus({ hours: Math.floor(Math.random() * 3) + 1 }).toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 30) + 10 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }},\n      \"status\": \"OFFLINE\",\n      \"vehicleType\": \"Motorcycle\",\n      \"plateNumber\": \"B {{ Math.floor(Math.random() * 9000) + 1000 }} GHI\"\n    }\n  ],\n  \"total\": 4,\n  \"page\": 1,\n  \"limit\": 100\n}"}, "id": "gps-devices-success", "name": "GPS Device List Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 401,\n  \"message\": \"Unauthorized - Invalid or missing <PERSON><PERSON> token\",\n  \"data\": null\n}", "responseCode": 401}, "id": "gps-devices-error", "name": "GPS Device List Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 560]}], "connections": {"GPS Login": {"main": [[{"node": "Validate GPS Login", "type": "main", "index": 0}]]}, "Validate GPS Login": {"main": [[{"node": "GPS Login Success", "type": "main", "index": 0}], [{"node": "GPS Login Error", "type": "main", "index": 0}]]}, "GPS Device List All": {"main": [[{"node": "Validate GPS Auth", "type": "main", "index": 0}]]}, "Validate GPS Auth": {"main": [[{"node": "GPS Device List Success", "type": "main", "index": 0}], [{"node": "GPS Device List Error", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}