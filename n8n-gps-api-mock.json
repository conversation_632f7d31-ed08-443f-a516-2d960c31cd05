{"name": "GPS API Mock", "nodes": [{"parameters": {"httpMethod": "POST", "path": "user/login", "responseMode": "responseNode", "options": {}}, "id": "gps-login-webhook", "name": "GPS Login", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "gps-login"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"message\": \"Login successful\",\n  \"data\": {\n    \"token\": \"gps_mock_token_{{ $now.format('x') }}\",\n    \"expires_in\": 3600,\n    \"user_id\": \"{{ $json.body.username || 'mock_user' }}\",\n    \"user_type\": \"admin\"\n  }\n}"}, "id": "gps-login-response", "name": "GPS Login Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"httpMethod": "POST", "path": "device/list_all", "responseMode": "responseNode", "options": {}}, "id": "gps-devices-webhook", "name": "GPS Device List", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 450], "webhookId": "gps-devices"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"message\": \"Devices retrieved successfully\",\n  \"data\": [\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-6) }}\",\n      \"imei\": \"86{{ $now.format('x').slice(-13) }}\",\n      \"name\": \"Mock GPS Device 1\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 80) + 20 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": true,\n      \"lastUpdate\": \"{{ $now.toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 40) + 60 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }}\n    },\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-5) }}1\",\n      \"imei\": \"86{{ $now.format('x').slice(-12) }}1\",\n      \"name\": \"Mock GPS Device 2\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 80) + 20 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": {{ Math.random() > 0.3 }},\n      \"lastUpdate\": \"{{ $now.minus({ minutes: Math.floor(Math.random() * 60) }).toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 50) + 30 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }}\n    },\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-5) }}2\",\n      \"imei\": \"86{{ $now.format('x').slice(-12) }}2\",\n      \"name\": \"Mock GPS Device 3\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 80) + 20 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": {{ Math.random() > 0.2 }},\n      \"lastUpdate\": \"{{ $now.minus({ minutes: Math.floor(Math.random() * 120) }).toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 60) + 40 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }}\n    }\n  ]\n}"}, "id": "gps-devices-response", "name": "GPS Device List Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 450]}], "connections": {"GPS Login": {"main": [[{"node": "GPS Login Response", "type": "main", "index": 0}]]}, "GPS Device List": {"main": [[{"node": "GPS Device List Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}