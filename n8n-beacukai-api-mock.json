{"name": "Beacukai API Mock - Sesuai Project", "nodes": [{"parameters": {"httpMethod": "GET", "path": "rest/pub/apigateway/jwt/getJsonWebToken", "responseMode": "responseNode", "options": {}}, "id": "jwt-token-webhook", "name": "JWT Token", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 200], "webhookId": "jwt-token"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.headers.authorization }}", "rightValue": "Basic dmlydHVzRGV2OmFSNyNwTDlxWkAxbQ==", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.query.app_id }}", "rightValue": "56c566b2-53ab-4335-8f13-efdbe144ba52", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-jwt-auth", "name": "Validate JWT Auth", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 200]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"jwt\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ2aXJ0dXNEZXYiLCJpYXQiOjE2MzQ1NjcwMDAsImV4cCI6MTYzNDU3MDYwMCwiYXBwX2lkIjoiNTZjNTY2YjItNTNhYi00MzM1LThmMTMtZWZkYmUxNDRiYTUyIn0.mock_signature_for_testing_{{ $now.format('x') }}\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\",\n  \"scope\": \"eseal_api\"\n}", "responseHeaders": {"entries": [{"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=mock_session_{{ $now.format('x') }}; Path=/; HttpOnly; Secure"}]}}, "id": "jwt-success-response", "name": "JWT Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 140]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"error\": \"Unauthorized\",\n  \"message\": \"Invalid credentials or app_id\"\n}", "responseCode": 401}, "id": "jwt-error-response", "name": "JWT Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 260]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add-webhook", "name": "E-Seal Add", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 380], "webhookId": "eseal-add"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "9614fc78-356d-4520-b05b-dd937f556ffw", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-eseal-add", "name": "Validate E-Seal Add", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 380]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"E-Seal berhasil ditambahkan ke sistem eMS\",\n  \"item\": {\n    \"idEseal\": \"ESEAL_{{ $now.format('x') }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"idVendor\": \"{{ $json.body.idVendor }}\",\n    \"merk\": \"{{ $json.body.merk || 'Default Brand' }}\",\n    \"model\": \"{{ $json.body.model || 'Default Model' }}\",\n    \"tipe\": \"{{ $json.body.tipe || 'GPS' }}\",\n    \"registeredAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"REGISTERED\"\n  }\n}"}, "id": "eseal-add-success", "name": "E-Seal Add Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 320]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"idVendor, token, atau noImei tidak valid. Pastikan menggunakan idVendor: 55028fd4-ba83-44a4-a506-c3c7b8401ffe dan token: 9614fc78-356d-4520-b05b-dd937f556ffw\",\n  \"item\": null\n}", "responseCode": 400}, "id": "eseal-add-error", "name": "E-Seal Add Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"httpMethod": "GET", "path": "dokumen-eseal-service/eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "dokumen-pabean-webhook", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 560], "webhookId": "dokumen-pabean"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.query.nomor_aju }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.query.nomor_aju.length }}", "rightValue": "26", "operator": {"type": "number", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-dokumen-pabean", "name": "Validate Dokumen <PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 560]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Dokumen pabean ditemukan\",\n  \"item\": {\n    \"nomorAju\": \"{{ $json.query.nomor_aju }}\",\n    \"kodeDokumen\": \"BC23\",\n    \"nomorDaftar\": \"{{ $json.query.nomor_aju }}/BC23/2024\",\n    \"tanggalDaftar\": \"{{ $now.format('yyyy-MM-dd') }}\",\n    \"kodeKantor\": \"040300\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe Madya Pabean Tanjung Priok\",\n    \"kodeTps\": \"JKTC\",\n    \"namaGudang\": \"PT Terminal Petikemas Jakarta\",\n    \"idPengusaha\": \"{{ $now.format('x').slice(-14) }}\",\n    \"namaPengusaha\": \"PT Mock Eksportir Indonesia\",\n    \"uraian\": \"Barang ekspor berupa tekstil dan garmen untuk testing\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"MOCK{{ $now.format('x').slice(-6) }}\",\n        \"nomorSegel\": \"SGL{{ $now.format('x').slice(-4) }}\"\n      }\n    ]\n  }\n}"}, "id": "dokumen-pabean-success", "name": "Do<PERSON>men <PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Parameter nomor_aju is required dan harus 26 karakter\",\n  \"item\": null\n}", "responseCode": 400}, "id": "dokumen-pabean-error", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 620]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/start", "responseMode": "responseNode", "options": {}}, "id": "tracking-start-webhook", "name": "Tracking Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 740], "webhookId": "tracking-start"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "9614fc78-356d-4520-b05b-dd937f556ffw", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-4", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-tracking-start", "name": "Validate Tracking Start", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 740]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dimulai\",\n  \"item\": {\n    \"trackingId\": \"TRK_{{ $now.format('x') }}\",\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"startedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"ACTIVE\",\n    \"route\": {\n      \"from\": {\n        \"address\": \"{{ $json.body.alamatAsal }}\",\n        \"location\": \"{{ $json.body.lokasiAsal }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeAsal }}\",\n          \"lng\": \"{{ $json.body.longitudeAsal }}\"\n        }\n      },\n      \"to\": {\n        \"address\": \"{{ $json.body.alamatTujuan }}\",\n        \"location\": \"{{ $json.body.lokasiTujuan }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeTujuan }}\",\n          \"lng\": \"{{ $json.body.longitudeTujuan }}\"\n        }\n      }\n    },\n    \"vehicle\": {\n      \"noPolisi\": \"{{ $json.body.noPolisi }}\",\n      \"driver\": {\n        \"name\": \"{{ $json.body.namaDriver }}\",\n        \"phone\": \"{{ $json.body.nomorTeleponDriver }}\"\n      },\n      \"container\": {\n        \"number\": \"{{ $json.body.noKontainer }}\",\n        \"type\": \"{{ $json.body.jnsKontainer }}\",\n        \"size\": \"{{ $json.body.ukKontainer }}\"\n      }\n    },\n    \"documents\": {{ JSON.stringify($json.body.dokumen || []) }}\n  }\n}"}, "id": "tracking-start-success", "name": "Tracking Start Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 680]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"idVendor, token, noEseal, atau noImei tidak valid\",\n  \"item\": null\n}", "responseCode": 400}, "id": "tracking-start-error", "name": "Tracking Start Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 800]}, {"parameters": {"httpMethod": "POST", "path": "position-eseal/eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "position-update-webhook", "name": "Position Update", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 920], "webhookId": "position-update"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "9614fc78-356d-4520-b05b-dd937f556ffw", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-4", "leftValue": "={{ $json.body.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-position-update", "name": "Validate Position Update", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 920]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Posisi E-Seal berhasil diupdate\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"position\": {\n      \"latitude\": \"{{ $json.body.latitude }}\",\n      \"longitude\": \"{{ $json.body.longitude }}\",\n      \"address\": \"{{ $json.body.address || 'Unknown Location' }}\",\n      \"altitude\": \"{{ $json.body.altitude || '0' }}\",\n      \"speed\": \"{{ $json.body.speed || '0' }}\",\n      \"city\": \"{{ $json.body.kota || 'Jakarta' }}\",\n      \"province\": \"{{ $json.body.provinsi || 'DKI Jakarta' }}\"\n    },\n    \"device\": {\n      \"battery\": \"{{ $json.body.battery || '80' }}\",\n      \"dayaAki\": \"{{ $json.body.dayaAki || '12.5' }}\",\n      \"event\": \"{{ $json.body.event || '0' }}\"\n    },\n    \"updatedAt\": \"{{ $now.toISOString() }}\",\n    \"processed\": true\n  }\n}"}, "id": "position-update-success", "name": "Position Update Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 860]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"idVendor, token, noImei, atau noEseal tidak valid\",\n  \"item\": null\n}", "responseCode": 400}, "id": "position-update-error", "name": "Position Update Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 980]}], "connections": {"JWT Token": {"main": [[{"node": "Validate JWT Auth", "type": "main", "index": 0}]]}, "Validate JWT Auth": {"main": [[{"node": "JWT Success", "type": "main", "index": 0}], [{"node": "JWT Error", "type": "main", "index": 0}]]}, "E-Seal Add": {"main": [[{"node": "Validate E-Seal Add", "type": "main", "index": 0}]]}, "Validate E-Seal Add": {"main": [[{"node": "E-Seal Add Success", "type": "main", "index": 0}], [{"node": "E-Seal Add Error", "type": "main", "index": 0}]]}, "Dokumen Pabean": {"main": [[{"node": "Validate Dokumen <PERSON>", "type": "main", "index": 0}]]}, "Validate Dokumen Pabean": {"main": [[{"node": "Do<PERSON>men <PERSON>", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Tracking Start": {"main": [[{"node": "Validate Tracking Start", "type": "main", "index": 0}]]}, "Validate Tracking Start": {"main": [[{"node": "Tracking Start Success", "type": "main", "index": 0}], [{"node": "Tracking Start Error", "type": "main", "index": 0}]]}, "Position Update": {"main": [[{"node": "Validate Position Update", "type": "main", "index": 0}]]}, "Validate Position Update": {"main": [[{"node": "Position Update Success", "type": "main", "index": 0}], [{"node": "Position Update Error", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}