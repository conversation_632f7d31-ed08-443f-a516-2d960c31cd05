{"name": "E-Seal Management Mock", "nodes": [{"parameters": {"httpMethod": "POST", "path": "tracking-eseal/eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add-webhook", "name": "E-Seal Add Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "eseal-add"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-3", "leftValue": "={{ $json.body.token }}", "rightValue": "919253c8-d0e1-4780-89d0-e91f77e89855", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-eseal-add", "name": "Validate E-Seal Add", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"E-Seal berhasil ditambahkan ke sistem eMS\",\n  \"item\": {\n    \"idEseal\": \"ESEAL_{{ $now.format('x') }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"idVendor\": \"{{ $json.body.idVendor }}\",\n    \"merk\": \"{{ $json.body.merk || 'Default Brand' }}\",\n    \"model\": \"{{ $json.body.model || 'Default Model' }}\",\n    \"tipe\": \"{{ $json.body.tipe || 'GPS' }}\",\n    \"registeredAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"REGISTERED\"\n  }\n}"}, "id": "eseal-add-success", "name": "E-Seal Add Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 240]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data E-Seal tidak lengkap atau token tidak valid\",\n  \"item\": null\n}", "responseCode": 400}, "id": "eseal-add-error", "name": "E-Seal Add Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 360]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/start", "responseMode": "responseNode", "options": {}}, "id": "tracking-start-webhook", "name": "Tracking Start Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500], "webhookId": "tracking-start"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-3", "leftValue": "={{ $json.body.token }}", "rightValue": "919253c8-d0e1-4780-89d0-e91f77e89855", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-tracking-start", "name": "Validate Tracking Start", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dimulai\",\n  \"item\": {\n    \"trackingId\": \"TRK_{{ $now.format('x') }}\",\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"startedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"ACTIVE\",\n    \"route\": {\n      \"from\": {\n        \"address\": \"{{ $json.body.alamatAsal }}\",\n        \"location\": \"{{ $json.body.lokasiAsal }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeAsal }}\",\n          \"lng\": \"{{ $json.body.longitudeAsal }}\"\n        }\n      },\n      \"to\": {\n        \"address\": \"{{ $json.body.alamatTujuan }}\",\n        \"location\": \"{{ $json.body.lokasiTujuan }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeTujuan }}\",\n          \"lng\": \"{{ $json.body.longitudeTujuan }}\"\n        }\n      }\n    },\n    \"vehicle\": {\n      \"noPolisi\": \"{{ $json.body.noPolisi }}\",\n      \"driver\": {\n        \"name\": \"{{ $json.body.namaDriver }}\",\n        \"phone\": \"{{ $json.body.nomorTeleponDriver }}\"\n      },\n      \"container\": {\n        \"number\": \"{{ $json.body.noKontainer }}\",\n        \"type\": \"{{ $json.body.jnsKontainer }}\",\n        \"size\": \"{{ $json.body.ukKontainer }}\"\n      }\n    },\n    \"documents\": {{ JSON.stringify($json.body.dokumen) }}\n  }\n}"}, "id": "tracking-start-success", "name": "Tracking Start Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data tracking tidak lengkap atau token tidak valid\",\n  \"item\": null\n}", "responseCode": 400}, "id": "tracking-start-error", "name": "Tracking Start Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 560]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/stop", "responseMode": "responseNode", "options": {}}, "id": "tracking-stop-webhook", "name": "Tracking Stop Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 700], "webhookId": "tracking-stop"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-tracking-stop", "name": "Validate Tracking Stop", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 700]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dihentikan\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"stoppedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"STOPPED\",\n    \"finalLocation\": {\n      \"address\": \"{{ $json.body.alamatStop }}\",\n      \"coordinates\": {\n        \"lat\": \"{{ $json.body.latitudeStop }}\",\n        \"lng\": \"{{ $json.body.longitudeStop }}\"\n      }\n    },\n    \"summary\": {\n      \"totalDistance\": \"{{ Math.floor(Math.random() * 500) + 100 }} km\",\n      \"totalTime\": \"{{ Math.floor(Math.random() * 12) + 2 }} hours\",\n      \"averageSpeed\": \"{{ Math.floor(Math.random() * 40) + 40 }} km/h\"\n    }\n  }\n}"}, "id": "tracking-stop-success", "name": "Tracking Stop Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 640]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data stop tracking tidak lengkap\",\n  \"item\": null\n}", "responseCode": 400}, "id": "tracking-stop-error", "name": "Tracking Stop Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 760]}], "connections": {"E-Seal Add Request": {"main": [[{"node": "Validate E-Seal Add", "type": "main", "index": 0}]]}, "Validate E-Seal Add": {"main": [[{"node": "E-Seal Add Success", "type": "main", "index": 0}], [{"node": "E-Seal Add Error", "type": "main", "index": 0}]]}, "Tracking Start Request": {"main": [[{"node": "Validate Tracking Start", "type": "main", "index": 0}]]}, "Validate Tracking Start": {"main": [[{"node": "Tracking Start Success", "type": "main", "index": 0}], [{"node": "Tracking Start Error", "type": "main", "index": 0}]]}, "Tracking Stop Request": {"main": [[{"node": "Validate Tracking Stop", "type": "main", "index": 0}]]}, "Validate Tracking Stop": {"main": [[{"node": "Tracking Stop Success", "type": "main", "index": 0}], [{"node": "Tracking Stop Error", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}