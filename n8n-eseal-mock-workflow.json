{"name": "E-Seal Monitor API Mock", "nodes": [{"parameters": {"httpMethod": "GET", "path": "health", "responseMode": "responseNode", "options": {}}, "id": "health-check", "name": "Health Check", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "health-check"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"N8N E-Seal Mock API is running\",\n  \"timestamp\": \"{{ $now }}\",\n  \"version\": \"1.0.0\"\n}"}, "id": "health-response", "name": "Health Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"httpMethod": "GET", "path": "rest/pub/apigateway/jwt/getJsonWebToken", "responseMode": "responseNode", "options": {}}, "id": "jwt-token", "name": "JWT Token", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 420], "webhookId": "jwt-token"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"jwt\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************.mock_signature_for_testing\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\",\n  \"scope\": \"eseal_api\"\n}", "responseHeaders": {"entries": [{"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=mock_session_{{ $now.format('x') }}; Path=/; HttpOnly"}]}}, "id": "jwt-response", "name": "JWT Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 420]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add", "name": "E-Seal Add", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 540], "webhookId": "eseal-add"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-eseal-data", "name": "Validate E-Seal Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 540]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"E-Seal berhasil ditambahkan\",\n  \"item\": {\n    \"idEseal\": \"ESEAL_{{ $now.format('x') }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"idVendor\": \"{{ $json.body.idVendor }}\",\n    \"merk\": \"{{ $json.body.merk || 'Default Brand' }}\",\n    \"model\": \"{{ $json.body.model || 'Default Model' }}\",\n    \"tipe\": \"{{ $json.body.tipe || 'GPS' }}\",\n    \"registeredAt\": \"{{ $now.toISOString() }}\"\n  }\n}"}, "id": "eseal-add-success", "name": "E-Seal Add Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 480]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data E-Seal tidak lengkap. noImei dan idVendor wajib diisi\",\n  \"item\": null\n}", "responseCode": 400}, "id": "eseal-add-error", "name": "E-Seal Add Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/start", "responseMode": "responseNode", "options": {}}, "id": "tracking-start", "name": "Tracking Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 720], "webhookId": "tracking-start"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dimulai\",\n  \"item\": {\n    \"trackingId\": \"TRK_{{ $now.format('x') }}\",\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"startedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"ACTIVE\",\n    \"route\": {\n      \"from\": {\n        \"address\": \"{{ $json.body.alamatAsal }}\",\n        \"location\": \"{{ $json.body.lokasiAsal }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeAsal }}\",\n          \"lng\": \"{{ $json.body.longitudeAsal }}\"\n        }\n      },\n      \"to\": {\n        \"address\": \"{{ $json.body.alamatTujuan }}\",\n        \"location\": \"{{ $json.body.lokasiTujuan }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeTujuan }}\",\n          \"lng\": \"{{ $json.body.longitudeTujuan }}\"\n        }\n      }\n    },\n    \"vehicle\": {\n      \"noPolisi\": \"{{ $json.body.noPolisi }}\",\n      \"driver\": {\n        \"name\": \"{{ $json.body.namaDriver }}\",\n        \"phone\": \"{{ $json.body.nomorTeleponDriver }}\"\n      },\n      \"container\": {\n        \"number\": \"{{ $json.body.noKontainer }}\",\n        \"type\": \"{{ $json.body.jnsKontainer }}\",\n        \"size\": \"{{ $json.body.ukKontainer }}\"\n      }\n    }\n  }\n}"}, "id": "tracking-start-response", "name": "Tracking Start Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 720]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/stop", "responseMode": "responseNode", "options": {}}, "id": "tracking-stop", "name": "Tracking Stop", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 840], "webhookId": "tracking-stop"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dihentikan\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"stoppedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"STOPPED\",\n    \"finalLocation\": {\n      \"address\": \"{{ $json.body.alamatStop }}\",\n      \"coordinates\": {\n        \"lat\": \"{{ $json.body.latitudeStop }}\",\n        \"lng\": \"{{ $json.body.longitudeStop }}\"\n      }\n    }\n  }\n}"}, "id": "tracking-stop-response", "name": "Tracking Stop Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 840]}, {"parameters": {"httpMethod": "POST", "path": "position-eseal/eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "position-update", "name": "Position Update", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 960], "webhookId": "position-update"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Posisi berhasil diupdate\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"position\": {\n      \"latitude\": \"{{ $json.body.latitude }}\",\n      \"longitude\": \"{{ $json.body.longitude }}\",\n      \"address\": \"{{ $json.body.address }}\",\n      \"altitude\": \"{{ $json.body.altitude }}\",\n      \"speed\": \"{{ $json.body.speed }}\",\n      \"city\": \"{{ $json.body.kota }}\",\n      \"province\": \"{{ $json.body.provinsi }}\"\n    },\n    \"device\": {\n      \"battery\": \"{{ $json.body.battery }}\",\n      \"dayaAki\": \"{{ $json.body.dayaAki }}\",\n      \"event\": \"{{ $json.body.event }}\"\n    },\n    \"updatedAt\": \"{{ $now.toISOString() }}\"\n  }\n}"}, "id": "position-update-response", "name": "Position Update Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 960]}, {"parameters": {"httpMethod": "GET", "path": "dokumen-eseal-service/eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "dokumen-pabean", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 1080], "webhookId": "dokumen-pabean"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Dokumen pabean ditemukan\",\n  \"item\": {\n    \"nomorAju\": \"{{ $json.query.nomor_aju }}\",\n    \"kodeDokumen\": \"BC23\",\n    \"nomorDaftar\": \"{{ $json.query.nomor_aju }}/BC23/2024\",\n    \"tanggalDaftar\": \"{{ $now.format('yyyy-MM-dd') }}\",\n    \"kodeKantor\": \"040300\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe Madya Pabean Tanjung Priok\",\n    \"kodeTps\": \"JKTC\",\n    \"namaGudang\": \"PT Terminal Petikemas Jakarta\",\n    \"idPengusaha\": \"12345678901234\",\n    \"namaPengusaha\": \"PT Contoh Eksportir Indonesia\",\n    \"uraian\": \"Barang ekspor berupa tekstil dan garmen\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"MOCK{{ $now.format('x').slice(-6) }}\",\n        \"nomorSegel\": \"SGL{{ $now.format('x').slice(-4) }}\"\n      }\n    ]\n  }\n}"}, "id": "dokumen-pabean-response", "name": "Dokumen <PERSON> Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 1080]}, {"parameters": {"httpMethod": "GET", "path": "tracking/status", "responseMode": "responseNode", "options": {}}, "id": "tracking-status", "name": "Tracking Status", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 1200], "webhookId": "tracking-status"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Status tracking ditemukan\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.query.noEseal }}\",\n    \"idVendor\": \"{{ $json.query.idVendor }}\",\n    \"trackingStatus\": \"ACTIVE\",\n    \"currentPosition\": {\n      \"latitude\": \"-6.{{ Math.floor(Math.random() * 900000) + 100000 }}\",\n      \"longitude\": \"106.{{ Math.floor(Math.random() * 900000) + 100000 }}\",\n      \"address\": \"Jakarta, Indonesia\",\n      \"timestamp\": \"{{ $now.toISOString() }}\"\n    },\n    \"journey\": {\n      \"startTime\": \"{{ $now.minus({ hours: 2 }).toISOString() }}\",\n      \"estimatedArrival\": \"{{ $now.plus({ hours: 4 }).toISOString() }}\",\n      \"distanceTraveled\": \"{{ Math.floor(Math.random() * 100) + 50 }} km\",\n      \"averageSpeed\": \"{{ Math.floor(Math.random() * 40) + 40 }} km/h\"\n    },\n    \"device\": {\n      \"battery\": \"{{ Math.floor(Math.random() * 40) + 60 }}%\",\n      \"signal\": \"Good\",\n      \"lastUpdate\": \"{{ $now.toISOString() }}\"\n    }\n  }\n}"}, "id": "tracking-status-response", "name": "Tracking Status Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 1200]}, {"parameters": {"httpMethod": "POST", "path": "gps/auth", "responseMode": "responseNode", "options": {}}, "id": "gps-auth", "name": "GPS Auth", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 1320], "webhookId": "gps-auth"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"message\": \"Authentication successful\",\n  \"data\": {\n    \"token\": \"gps_mock_token_{{ $now.format('x') }}\",\n    \"expires_in\": 3600,\n    \"user_id\": \"mock_user\"\n  }\n}"}, "id": "gps-auth-response", "name": "GPS Auth Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 1320]}, {"parameters": {"httpMethod": "POST", "path": "device/list_all", "responseMode": "responseNode", "options": {}}, "id": "gps-devices", "name": "GPS Devices", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 1440], "webhookId": "gps-devices"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\": 200,\n  \"message\": \"Devices retrieved successfully\",\n  \"data\": [\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-6) }}\",\n      \"imei\": \"86{{ $now.format('x').slice(-13) }}\",\n      \"name\": \"Mock GPS Device 1\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 80) + 20 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": true,\n      \"lastUpdate\": \"{{ $now.toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 40) + 60 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }}\n    },\n    {\n      \"deviceId\": \"DEV{{ $now.format('x').slice(-5) }}1\",\n      \"imei\": \"86{{ $now.format('x').slice(-12) }}1\",\n      \"name\": \"Mock GPS Device 2\",\n      \"lat\": -6.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"lng\": 106.{{ Math.floor(Math.random() * 900000) + 100000 }},\n      \"speed\": {{ Math.floor(Math.random() * 80) + 20 }},\n      \"direction\": {{ Math.floor(Math.random() * 360) }},\n      \"altitude\": {{ Math.floor(Math.random() * 100) + 10 }},\n      \"online\": false,\n      \"lastUpdate\": \"{{ $now.minus({ minutes: 30 }).toISOString() }}\",\n      \"devBatteryPCT\": {{ Math.floor(Math.random() * 30) + 20 }},\n      \"mileage\": {{ Math.floor(Math.random() * 10000) + 1000 }}\n    }\n  ]\n}"}, "id": "gps-devices-response", "name": "GPS Devices Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 1440]}], "connections": {"Health Check": {"main": [[{"node": "Health Response", "type": "main", "index": 0}]]}, "JWT Token": {"main": [[{"node": "JWT Response", "type": "main", "index": 0}]]}, "E-Seal Add": {"main": [[{"node": "Validate E-Seal Data", "type": "main", "index": 0}]]}, "Validate E-Seal Data": {"main": [[{"node": "E-Seal Add Success", "type": "main", "index": 0}], [{"node": "E-Seal Add Error", "type": "main", "index": 0}]]}, "Tracking Start": {"main": [[{"node": "Tracking Start Response", "type": "main", "index": 0}]]}, "Tracking Stop": {"main": [[{"node": "Tracking Stop Response", "type": "main", "index": 0}]]}, "Position Update": {"main": [[{"node": "Position Update Response", "type": "main", "index": 0}]]}, "Dokumen Pabean": {"main": [[{"node": "Dokumen <PERSON> Response", "type": "main", "index": 0}]]}, "Tracking Status": {"main": [[{"node": "Tracking Status Response", "type": "main", "index": 0}]]}, "GPS Auth": {"main": [[{"node": "GPS Auth Response", "type": "main", "index": 0}]]}, "GPS Devices": {"main": [[{"node": "GPS Devices Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}