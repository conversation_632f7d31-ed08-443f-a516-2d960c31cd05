{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "isolatedModules": true, "moduleDetection": "force", "jsx": "react-jsx", "composite": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["../shared/src/*"], "@shared": ["../shared/src"]}}, "include": ["src", "../shared/src"], "types": ["vite/client"], "files": ["src/types/images.d.ts"]}