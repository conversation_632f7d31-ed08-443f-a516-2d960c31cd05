{"name": "Position & Status Mock", "nodes": [{"parameters": {"httpMethod": "POST", "path": "position-eseal/eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "position-update-webhook", "name": "Position Update Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "position-update"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.body.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-3", "leftValue": "={{ $json.body.latitude }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-4", "leftValue": "={{ $json.body.longitude }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-position-update", "name": "Validate Position Update", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Posisi E-Seal berhasil diupdate\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"position\": {\n      \"latitude\": \"{{ $json.body.latitude }}\",\n      \"longitude\": \"{{ $json.body.longitude }}\",\n      \"address\": \"{{ $json.body.address || 'Unknown Location' }}\",\n      \"altitude\": \"{{ $json.body.altitude || '0' }}\",\n      \"speed\": \"{{ $json.body.speed || '0' }}\",\n      \"city\": \"{{ $json.body.kota || 'Jakarta' }}\",\n      \"province\": \"{{ $json.body.provinsi || 'DKI Jakarta' }}\"\n    },\n    \"device\": {\n      \"battery\": \"{{ $json.body.battery || '80' }}%\",\n      \"dayaAki\": \"{{ $json.body.dayaAki || '12.5' }}V\",\n      \"event\": \"{{ $json.body.event || '0' }}\",\n      \"signal\": \"Good\"\n    },\n    \"updatedAt\": \"{{ $now.toISOString() }}\",\n    \"processed\": true\n  }\n}"}, "id": "position-update-success", "name": "Position Update Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 240]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Data posisi tidak lengkap. noImei, noEseal, latitude, dan longitude wajib diisi\",\n  \"item\": null\n}", "responseCode": 400}, "id": "position-update-error", "name": "Position Update Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 360]}, {"parameters": {"httpMethod": "GET", "path": "tracking/status", "responseMode": "responseNode", "options": {}}, "id": "tracking-status-webhook", "name": "Tracking Status Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500], "webhookId": "tracking-status"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.query.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.query.idVendor }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-tracking-status", "name": "Validate Tracking Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Status tracking ditemukan\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.query.noEseal }}\",\n    \"idVendor\": \"{{ $json.query.idVendor }}\",\n    \"trackingStatus\": \"ACTIVE\",\n    \"currentPosition\": {\n      \"latitude\": \"-6.{{ Math.floor(Math.random() * 900000) + 100000 }}\",\n      \"longitude\": \"106.{{ Math.floor(Math.random() * 900000) + 100000 }}\",\n      \"address\": \"Jl. Mock Street No. {{ Math.floor(Math.random() * 100) + 1 }}, Jakarta\",\n      \"timestamp\": \"{{ $now.toISOString() }}\",\n      \"accuracy\": \"{{ Math.floor(Math.random() * 10) + 5 }}m\"\n    },\n    \"journey\": {\n      \"startTime\": \"{{ $now.minus({ hours: Math.floor(Math.random() * 8) + 1 }).toISOString() }}\",\n      \"estimatedArrival\": \"{{ $now.plus({ hours: Math.floor(Math.random() * 6) + 2 }).toISOString() }}\",\n      \"distanceTraveled\": \"{{ Math.floor(Math.random() * 200) + 50 }} km\",\n      \"remainingDistance\": \"{{ Math.floor(Math.random() * 150) + 25 }} km\",\n      \"averageSpeed\": \"{{ Math.floor(Math.random() * 40) + 40 }} km/h\",\n      \"currentSpeed\": \"{{ Math.floor(Math.random() * 80) + 20 }} km/h\"\n    },\n    \"device\": {\n      \"battery\": \"{{ Math.floor(Math.random() * 40) + 60 }}%\",\n      \"signal\": \"{{ ['Excellent', 'Good', 'Fair'][Math.floor(Math.random() * 3)] }}\",\n      \"temperature\": \"{{ Math.floor(Math.random() * 15) + 25 }}°C\",\n      \"lastUpdate\": \"{{ $now.toISOString() }}\",\n      \"online\": true\n    },\n    \"alerts\": [\n      {\n        \"type\": \"INFO\",\n        \"message\": \"Device operating normally\",\n        \"timestamp\": \"{{ $now.toISOString() }}\"\n      }\n    ]\n  }\n}"}, "id": "tracking-status-success", "name": "Tracking Status Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Parameter noEseal dan idVendor wajib diisi\",\n  \"item\": null\n}", "responseCode": 400}, "id": "tracking-status-error", "name": "Tracking Status Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 560]}], "connections": {"Position Update Request": {"main": [[{"node": "Validate Position Update", "type": "main", "index": 0}]]}, "Validate Position Update": {"main": [[{"node": "Position Update Success", "type": "main", "index": 0}], [{"node": "Position Update Error", "type": "main", "index": 0}]]}, "Tracking Status Request": {"main": [[{"node": "Validate Tracking Status", "type": "main", "index": 0}]]}, "Validate Tracking Status": {"main": [[{"node": "Tracking Status Success", "type": "main", "index": 0}], [{"node": "Tracking Status Error", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}