{"name": "Beacukai Complete Mock - Save to DB + API Response", "nodes": [{"parameters": {"httpMethod": "GET", "path": "rest/pub/apigateway/jwt/getJsonWebToken", "responseMode": "responseNode", "options": {}}, "id": "jwt-token-webhook", "name": "JWT Token Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 200], "webhookId": "jwt-token"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.headers.authorization }}", "rightValue": "Basic dmlydHVzRGV2OmFSNyNwTDlxWkAxbQ==", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.query.app_id }}", "rightValue": "56c566b2-53ab-4335-8f13-efdbe144ba52", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-jwt-auth", "name": "Validate JWT Auth", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 200]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"jwt\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ2aXJ0dXNEZXYiLCJpYXQiOjE2MzQ1NjcwMDAsImV4cCI6MTYzNDU3MDYwMCwiYXBwX2lkIjoiNTZjNTY2YjItNTNhYi00MzM1LThmMTMtZWZkYmUxNDRiYTUyIn0.mock_signature_{{ $now.format('x') }}\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\",\n  \"scope\": \"eseal_api\"\n}", "responseHeaders": {"entries": [{"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=mock_session_{{ $now.format('x') }}; Path=/; HttpOnly"}]}}, "id": "jwt-success-response", "name": "JWT Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 140]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"error\": \"Unauthorized\",\n  \"message\": \"Invalid credentials or app_id\"\n}", "responseCode": 401}, "id": "jwt-error-response", "name": "JWT Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 260]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add-webhook", "name": "E-Seal Add Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 380], "webhookId": "eseal-add"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "9614fc78-356d-4520-b05b-dd937f556ffw", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-eseal-add", "name": "Validate E-Seal Add", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 380]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO request_logs (id, \"requestUrl\", \"requestBody\", \"requestHeader\", status, \"responseBody\", \"isSync\", \"createdAt\") VALUES (gen_random_uuid(), 'https://apisdev-gw.beacukai.go.id/tracking-eseal/eseal/add', $1::jsonb, $2::jsonb, 200, $3::jsonb, true, NOW())", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": "={{ JSON.stringify([$json.body, $json.headers, {\"status\": \"success\", \"message\": \"E-Seal berhasil ditambahkan\", \"item\": {\"idEseal\": \"ESEAL_\" + $now.format('x'), \"noImei\": $json.body.noImei, \"idVendor\": $json.body.idVendor}}]) }}"}}, "id": "save-eseal-add-log", "name": "Save E-Seal Add Log to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 320], "credentials": {"postgres": {"id": "postgres-db", "name": "PostgreSQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"E-Seal berhasil ditambahkan ke sistem eMS\",\n  \"item\": {\n    \"idEseal\": \"ESEAL_{{ $now.format('x') }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"idVendor\": \"{{ $json.body.idVendor }}\",\n    \"merk\": \"{{ $json.body.merk || 'Default Brand' }}\",\n    \"model\": \"{{ $json.body.model || 'Default Model' }}\",\n    \"tipe\": \"{{ $json.body.tipe || 'GPS' }}\",\n    \"registeredAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"REGISTERED\"\n  }\n}"}, "id": "eseal-add-success-response", "name": "E-Seal Add Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 320]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"idVendor, token, atau noImei tidak valid. Pastikan menggunakan idVendor: 55028fd4-ba83-44a4-a506-c3c7b8401ffe dan token: 9614fc78-356d-4520-b05b-dd937f556ffw\",\n  \"item\": null\n}", "responseCode": 400}, "id": "eseal-add-error-response", "name": "E-Seal Add Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"httpMethod": "GET", "path": "dokumen-eseal-service/eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "dokumen-pabean-webhook", "name": "Dokumen Pabean Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 560], "webhookId": "dokumen-pabean"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.query.nomor_aju }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "condition-2", "leftValue": "={{ $json.query.nomor_aju.length }}", "rightValue": "26", "operator": {"type": "number", "operation": "equals"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-dokumen-pabean", "name": "Validate Dokumen <PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 560]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO request_logs (id, \"requestUrl\", \"requestBody\", \"requestHeader\", status, \"responseBody\", \"isSync\", \"createdAt\") VALUES (gen_random_uuid(), 'https://apisdev-gw.beacukai.go.id/dokumen-eseal-service/eseal/get-dok-pabean', NULL, $1::jsonb, 200, $2::jsonb, true, NOW())", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": "={{ JSON.stringify([$json.headers, {\"status\": \"success\", \"message\": \"Dokumen pabean ditemukan\", \"item\": {\"nomorAju\": $json.query.nomor_aju, \"kodeDokumen\": \"BC23\"}}]) }}"}}, "id": "save-dokumen-log", "name": "Save <PERSON><PERSON><PERSON>g to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 500], "credentials": {"postgres": {"id": "postgres-db", "name": "PostgreSQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Dokumen pabean ditemukan\",\n  \"item\": {\n    \"nomorAju\": \"{{ $json.query.nomor_aju }}\",\n    \"kodeDokumen\": \"BC23\",\n    \"nomorDaftar\": \"{{ $json.query.nomor_aju }}/BC23/2024\",\n    \"tanggalDaftar\": \"{{ $now.format('yyyy-MM-dd') }}\",\n    \"kodeKantor\": \"040300\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe Madya Pabean Tanjung Priok\",\n    \"kodeTps\": \"JKTC\",\n    \"namaGudang\": \"PT Terminal Petikemas Jakarta\",\n    \"idPengusaha\": \"{{ $now.format('x').slice(-14) }}\",\n    \"namaPengusaha\": \"PT Mock Eksportir Indonesia\",\n    \"uraian\": \"Barang ekspor berupa tekstil dan garmen\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"MOCK{{ $now.format('x').slice(-6) }}\",\n        \"nomorSegel\": \"SGL{{ $now.format('x').slice(-4) }}\"\n      }\n    ]\n  }\n}"}, "id": "dokumen-pabean-success-response", "name": "Dokumen Pabean Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Parameter nomor_aju is required dan harus 26 karakter\",\n  \"item\": null\n}", "responseCode": 400}, "id": "dokumen-pabean-error-response", "name": "<PERSON><PERSON><PERSON> Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 620]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/start", "responseMode": "responseNode", "options": {}}, "id": "tracking-start-webhook", "name": "Tracking Start Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 740], "webhookId": "tracking-start"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "9614fc78-356d-4520-b05b-dd937f556ffw", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noEseal }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-tracking-start", "name": "Validate Tracking Start", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 740]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO tracking_session (id, \"esealId\", \"sessionStatus\", \"startedAt\", \"beacukaiStartStatus\", \"totalUpdates\", \"createdAt\", \"updatedAt\") SELECT gen_random_uuid(), e.id, 'ACTIVE', NOW(), 'success', 0, NOW(), NOW() FROM eseal e WHERE e.\"noEseal\" = $1 LIMIT 1", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": "={{ JSON.stringify([$json.body.noEseal]) }}"}}, "id": "save-tracking-session", "name": "Save Tracking Session to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 680], "credentials": {"postgres": {"id": "postgres-db", "name": "PostgreSQL Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO request_logs (id, \"requestUrl\", \"requestBody\", \"requestHeader\", status, \"responseBody\", \"isSync\", \"createdAt\") VALUES (gen_random_uuid(), 'https://apisdev-gw.beacukai.go.id/tracking-eseal/tracking/start', $1::jsonb, $2::jsonb, 200, $3::jsonb, true, NOW())", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": "={{ JSON.stringify([$json.body, $json.headers, {\"status\": \"success\", \"message\": \"Tracking berhasil dimulai\", \"item\": {\"trackingId\": \"TRK_\" + $now.format('x'), \"noEseal\": $json.body.noEseal}}]) }}"}}, "id": "save-tracking-start-log", "name": "Save Tracking Start Log to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 680], "credentials": {"postgres": {"id": "postgres-db", "name": "PostgreSQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dimulai\",\n  \"item\": {\n    \"trackingId\": \"TRK_{{ $now.format('x') }}\",\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"startedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"ACTIVE\",\n    \"route\": {\n      \"from\": {\n        \"address\": \"{{ $json.body.alamatAsal }}\",\n        \"location\": \"{{ $json.body.lokasiAsal }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeAsal }}\",\n          \"lng\": \"{{ $json.body.longitudeAsal }}\"\n        }\n      },\n      \"to\": {\n        \"address\": \"{{ $json.body.alamatTujuan }}\",\n        \"location\": \"{{ $json.body.lokasiTujuan }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeTujuan }}\",\n          \"lng\": \"{{ $json.body.longitudeTujuan }}\"\n        }\n      }\n    },\n    \"vehicle\": {\n      \"noPolisi\": \"{{ $json.body.noPolisi }}\",\n      \"driver\": {\n        \"name\": \"{{ $json.body.namaDriver }}\",\n        \"phone\": \"{{ $json.body.nomorTeleponDriver }}\"\n      },\n      \"container\": {\n        \"number\": \"{{ $json.body.noKontainer }}\",\n        \"type\": \"{{ $json.body.jnsKontainer }}\",\n        \"size\": \"{{ $json.body.ukKontainer }}\"\n      }\n    },\n    \"documents\": {{ JSON.stringify($json.body.dokumen || []) }}\n  }\n}"}, "id": "tracking-start-success-response", "name": "Tracking Start Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 680]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"idVendor, token, atau noEseal tidak valid\",\n  \"item\": null\n}", "responseCode": 400}, "id": "tracking-start-error-response", "name": "Tracking Start Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 800]}, {"parameters": {"httpMethod": "POST", "path": "position-eseal/eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "position-update-webhook", "name": "Position Update Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 920], "webhookId": "position-update"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.idVendor }}", "rightValue": "55028fd4-ba83-44a4-a506-c3c7b8401ffe", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-2", "leftValue": "={{ $json.body.token }}", "rightValue": "9614fc78-356d-4520-b05b-dd937f556ffw", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition-3", "leftValue": "={{ $json.body.noImei }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-position-update", "name": "Validate Position Update", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 920]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO tracking_log (id, \"esealId\", latitude, longitude, address, altitude, speed, battery, \"dayaAki\", event, kota, provinsi, \"apiResponse\", \"beacukaiStatus\", \"createdAt\") SELECT gen_random_uuid(), e.id, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11::jsonb, 'success', NOW() FROM eseal e WHERE e.\"noImei\" = $12 LIMIT 1", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": "={{ JSON.stringify([$json.body.latitude, $json.body.longitude, $json.body.address || 'Unknown Location', $json.body.altitude || '0', $json.body.speed || '0', $json.body.battery || '80', $json.body.dayaAki || '12.5', $json.body.event || '0', $json.body.kota || 'Jakarta', $json.body.provinsi || 'DKI Jakarta', $json.body, $json.body.noImei]) }}"}}, "id": "save-tracking-log", "name": "Save Tracking Log to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 860], "credentials": {"postgres": {"id": "postgres-db", "name": "PostgreSQL Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO request_logs (id, \"requestUrl\", \"requestBody\", \"requestHeader\", status, \"responseBody\", \"isSync\", \"createdAt\") VALUES (gen_random_uuid(), 'https://apisdev-gw.beacukai.go.id/position-eseal/eseal/update-position', $1::jsonb, $2::jsonb, 200, $3::jsonb, true, NOW())", "additionalFields": {"mode": "independently"}, "options": {"queryParameters": "={{ JSON.stringify([$json.body, $json.headers, {\"status\": \"success\", \"message\": \"Posisi berhasil diupdate\", \"item\": {\"noEseal\": $json.body.noEseal, \"noImei\": $json.body.noImei}}]) }}"}}, "id": "save-position-update-log", "name": "Save Position Update Log to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [900, 860], "credentials": {"postgres": {"id": "postgres-db", "name": "PostgreSQL Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Posisi E-Seal berhasil diupdate\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"position\": {\n      \"latitude\": \"{{ $json.body.latitude }}\",\n      \"longitude\": \"{{ $json.body.longitude }}\",\n      \"address\": \"{{ $json.body.address || 'Unknown Location' }}\",\n      \"altitude\": \"{{ $json.body.altitude || '0' }}\",\n      \"speed\": \"{{ $json.body.speed || '0' }}\",\n      \"city\": \"{{ $json.body.kota || 'Jakarta' }}\",\n      \"province\": \"{{ $json.body.provinsi || 'DKI Jakarta' }}\"\n    },\n    \"device\": {\n      \"battery\": \"{{ $json.body.battery || '80' }}\",\n      \"dayaAki\": \"{{ $json.body.dayaAki || '12.5' }}\",\n      \"event\": \"{{ $json.body.event || '0' }}\"\n    },\n    \"updatedAt\": \"{{ $now.toISOString() }}\",\n    \"processed\": true\n  }\n}"}, "id": "position-update-success-response", "name": "Position Update Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 860]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"idVendor, token, atau noImei tidak valid\",\n  \"item\": null\n}", "responseCode": 400}, "id": "position-update-error-response", "name": "Position Update Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 980]}], "connections": {"JWT Token Request": {"main": [[{"node": "Validate JWT Auth", "type": "main", "index": 0}]]}, "Validate JWT Auth": {"main": [[{"node": "JWT Success Response", "type": "main", "index": 0}], [{"node": "JWT Error Response", "type": "main", "index": 0}]]}, "E-Seal Add Request": {"main": [[{"node": "Validate E-Seal Add", "type": "main", "index": 0}]]}, "Validate E-Seal Add": {"main": [[{"node": "Save E-Seal Add Log to DB", "type": "main", "index": 0}], [{"node": "E-Seal Add Error Response", "type": "main", "index": 0}]]}, "Save E-Seal Add Log to DB": {"main": [[{"node": "E-Seal Add Success Response", "type": "main", "index": 0}]]}, "Dokumen Pabean Request": {"main": [[{"node": "Validate Dokumen <PERSON>", "type": "main", "index": 0}]]}, "Validate Dokumen Pabean": {"main": [[{"node": "Save <PERSON><PERSON><PERSON>g to DB", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON> Error Response", "type": "main", "index": 0}]]}, "Save Dokumen Log to DB": {"main": [[{"node": "Dokumen Pabean Success Response", "type": "main", "index": 0}]]}, "Tracking Start Request": {"main": [[{"node": "Validate Tracking Start", "type": "main", "index": 0}]]}, "Validate Tracking Start": {"main": [[{"node": "Save Tracking Session to DB", "type": "main", "index": 0}], [{"node": "Tracking Start Error Response", "type": "main", "index": 0}]]}, "Save Tracking Session to DB": {"main": [[{"node": "Save Tracking Start Log to DB", "type": "main", "index": 0}]]}, "Save Tracking Start Log to DB": {"main": [[{"node": "Tracking Start Success Response", "type": "main", "index": 0}]]}, "Position Update Request": {"main": [[{"node": "Validate Position Update", "type": "main", "index": 0}]]}, "Validate Position Update": {"main": [[{"node": "Save Tracking Log to DB", "type": "main", "index": 0}], [{"node": "Position Update Error Response", "type": "main", "index": 0}]]}, "Save Tracking Log to DB": {"main": [[{"node": "Save Position Update Log to DB", "type": "main", "index": 0}]]}, "Save Position Update Log to DB": {"main": [[{"node": "Position Update Success Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}