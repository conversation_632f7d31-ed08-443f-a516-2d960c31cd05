{"name": "Beacukai Complete API Mock", "nodes": [{"parameters": {"httpMethod": "GET", "path": "rest/pub/apigateway/jwt/getJsonWebToken", "responseMode": "responseNode", "options": {}}, "id": "jwt-webhook", "name": "JWT Token", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 200], "webhookId": "jwt-token"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"jwt\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ2aXJ0dXNEZXYiLCJpYXQiOjE2MzQ1NjcwMDAsImV4cCI6MTYzNDU3MDYwMCwiYXBwX2lkIjoiNTZjNTY2YjItNTNhYi00MzM1LThmMTMtZWZkYmUxNDRiYTUyIn0.mock_signature_{{ $now.format('x') }}\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\",\n  \"scope\": \"eseal_api\"\n}", "responseHeaders": {"entries": [{"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=mock_session_{{ $now.format('x') }}; Path=/; HttpOnly"}]}}, "id": "jwt-response", "name": "JWT Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 200]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/eseal/add", "responseMode": "responseNode", "options": {}}, "id": "eseal-add-webhook", "name": "E-Seal Add", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 320], "webhookId": "eseal-add"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"E-Seal berhasil ditambahkan ke sistem eMS\",\n  \"item\": {\n    \"idEseal\": \"ESEAL_{{ $now.format('x') }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"idVendor\": \"{{ $json.body.idVendor }}\",\n    \"merk\": \"{{ $json.body.merk }}\",\n    \"model\": \"{{ $json.body.model }}\",\n    \"tipe\": \"{{ $json.body.tipe }}\",\n    \"registeredAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"REGISTERED\"\n  }\n}"}, "id": "eseal-add-response", "name": "E-Seal Add Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 320]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/start", "responseMode": "responseNode", "options": {}}, "id": "tracking-start-webhook", "name": "Tracking Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 440], "webhookId": "tracking-start"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dimulai\",\n  \"item\": {\n    \"trackingId\": \"TRK_{{ $now.format('x') }}\",\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"startedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"ACTIVE\",\n    \"route\": {\n      \"from\": {\n        \"address\": \"{{ $json.body.alamatAsal }}\",\n        \"location\": \"{{ $json.body.lokasiAsal }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeAsal }}\",\n          \"lng\": \"{{ $json.body.longitudeAsal }}\"\n        }\n      },\n      \"to\": {\n        \"address\": \"{{ $json.body.alamatTujuan }}\",\n        \"location\": \"{{ $json.body.lokasiTujuan }}\",\n        \"coordinates\": {\n          \"lat\": \"{{ $json.body.latitudeTujuan }}\",\n          \"lng\": \"{{ $json.body.longitudeTujuan }}\"\n        }\n      }\n    },\n    \"vehicle\": {\n      \"noPolisi\": \"{{ $json.body.noPolisi }}\",\n      \"driver\": {\n        \"name\": \"{{ $json.body.namaDriver }}\",\n        \"phone\": \"{{ $json.body.nomorTeleponDriver }}\"\n      },\n      \"container\": {\n        \"number\": \"{{ $json.body.noKontainer }}\",\n        \"type\": \"{{ $json.body.jnsKontainer }}\",\n        \"size\": \"{{ $json.body.ukKontainer }}\"\n      }\n    },\n    \"documents\": {{ JSON.stringify($json.body.dokumen || []) }}\n  }\n}"}, "id": "tracking-start-response", "name": "Tracking Start Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 440]}, {"parameters": {"httpMethod": "POST", "path": "tracking-eseal/tracking/stop", "responseMode": "responseNode", "options": {}}, "id": "tracking-stop-webhook", "name": "Tracking Stop", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 560], "webhookId": "tracking-stop"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Tracking berhasil dihentikan\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"stoppedAt\": \"{{ $now.toISOString() }}\",\n    \"status\": \"STOPPED\",\n    \"finalLocation\": {\n      \"address\": \"{{ $json.body.alamatStop }}\",\n      \"coordinates\": {\n        \"lat\": \"{{ $json.body.latitudeStop }}\",\n        \"lng\": \"{{ $json.body.longitudeStop }}\"\n      }\n    }\n  }\n}"}, "id": "tracking-stop-response", "name": "Tracking Stop Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 560]}, {"parameters": {"httpMethod": "POST", "path": "position-eseal/eseal/update-position", "responseMode": "responseNode", "options": {}}, "id": "position-update-webhook", "name": "Position Update", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 680], "webhookId": "position-update"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Posisi E-Seal berhasil diupdate\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.body.noEseal }}\",\n    \"noImei\": \"{{ $json.body.noImei }}\",\n    \"position\": {\n      \"latitude\": \"{{ $json.body.latitude }}\",\n      \"longitude\": \"{{ $json.body.longitude }}\",\n      \"address\": \"{{ $json.body.address }}\",\n      \"altitude\": \"{{ $json.body.altitude }}\",\n      \"speed\": \"{{ $json.body.speed }}\",\n      \"city\": \"{{ $json.body.kota }}\",\n      \"province\": \"{{ $json.body.provinsi }}\"\n    },\n    \"device\": {\n      \"battery\": \"{{ $json.body.battery }}\",\n      \"dayaAki\": \"{{ $json.body.dayaAki }}\",\n      \"event\": \"{{ $json.body.event }}\"\n    },\n    \"updatedAt\": \"{{ $now.toISOString() }}\"\n  }\n}"}, "id": "position-update-response", "name": "Position Update Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 680]}, {"parameters": {"httpMethod": "GET", "path": "dokumen-eseal-service/eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "dokumen-pabean-webhook", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 800], "webhookId": "dokumen-pabean"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Dokumen pabean ditemukan\",\n  \"item\": {\n    \"nomorAju\": \"{{ $json.query.nomor_aju }}\",\n    \"kodeDokumen\": \"BC23\",\n    \"nomorDaftar\": \"{{ $json.query.nomor_aju }}/BC23/2024\",\n    \"tanggalDaftar\": \"{{ $now.format('yyyy-MM-dd') }}\",\n    \"kodeKantor\": \"040300\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe Madya Pabean Tanjung Priok\",\n    \"kodeTps\": \"JKTC\",\n    \"namaGudang\": \"PT Terminal Petikemas Jakarta\",\n    \"idPengusaha\": \"{{ $now.format('x').slice(-14) }}\",\n    \"namaPengusaha\": \"PT Mock Eksportir Indonesia\",\n    \"uraian\": \"Barang ekspor berupa tekstil dan garmen\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"MOCK{{ $now.format('x').slice(-6) }}\",\n        \"nomorSegel\": \"SGL{{ $now.format('x').slice(-4) }}\"\n      }\n    ]\n  }\n}"}, "id": "dokumen-pabean-response", "name": "Dokumen <PERSON> Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 800]}, {"parameters": {"httpMethod": "GET", "path": "tracking/status", "responseMode": "responseNode", "options": {}}, "id": "tracking-status-webhook", "name": "Tracking Status", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 920], "webhookId": "tracking-status"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Status tracking ditemukan\",\n  \"item\": {\n    \"noEseal\": \"{{ $json.query.noEseal }}\",\n    \"idVendor\": \"{{ $json.query.idVendor }}\",\n    \"trackingStatus\": \"ACTIVE\",\n    \"currentPosition\": {\n      \"latitude\": \"-6.{{ Math.floor(Math.random() * 900000) + 100000 }}\",\n      \"longitude\": \"106.{{ Math.floor(Math.random() * 900000) + 100000 }}\",\n      \"address\": \"Jakarta, Indonesia\",\n      \"timestamp\": \"{{ $now.toISOString() }}\"\n    },\n    \"journey\": {\n      \"startTime\": \"{{ $now.minus({ hours: 2 }).toISOString() }}\",\n      \"estimatedArrival\": \"{{ $now.plus({ hours: 4 }).toISOString() }}\",\n      \"distanceTraveled\": \"{{ Math.floor(Math.random() * 100) + 50 }} km\",\n      \"averageSpeed\": \"{{ Math.floor(Math.random() * 40) + 40 }} km/h\"\n    },\n    \"device\": {\n      \"battery\": \"{{ Math.floor(Math.random() * 40) + 60 }}%\",\n      \"signal\": \"Good\",\n      \"lastUpdate\": \"{{ $now.toISOString() }}\"\n    }\n  }\n}"}, "id": "tracking-status-response", "name": "Tracking Status Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 920]}], "connections": {"JWT Token": {"main": [[{"node": "JWT Response", "type": "main", "index": 0}]]}, "E-Seal Add": {"main": [[{"node": "E-Seal Add Response", "type": "main", "index": 0}]]}, "Tracking Start": {"main": [[{"node": "Tracking Start Response", "type": "main", "index": 0}]]}, "Tracking Stop": {"main": [[{"node": "Tracking Stop Response", "type": "main", "index": 0}]]}, "Position Update": {"main": [[{"node": "Position Update Response", "type": "main", "index": 0}]]}, "Dokumen Pabean": {"main": [[{"node": "Dokumen <PERSON> Response", "type": "main", "index": 0}]]}, "Tracking Status": {"main": [[{"node": "Tracking Status Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}