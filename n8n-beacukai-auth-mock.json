{"name": "Beacukai Complete API Mock", "nodes": [{"parameters": {"httpMethod": "GET", "path": "rest/pub/apigateway/jwt/getJsonWebToken", "responseMode": "responseNode", "options": {}}, "id": "jwt-token-webhook", "name": "JWT Token Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "jwt-token"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.headers.authorization }}", "rightValue": "Basic", "operator": {"type": "string", "operation": "contains"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-auth", "name": "Valida<PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"jwt\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ2aXJ0dXNEZXYiLCJpYXQiOjE2MzQ1NjcwMDAsImV4cCI6MTYzNDU3MDYwMCwiYXBwX2lkIjoiNTZjNTY2YjItNTNhYi00MzM1LThmMTMtZWZkYmUxNDRiYTUyIn0.mock_signature_for_testing_{{ $now.format('x') }}\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\",\n  \"scope\": \"eseal_api\"\n}", "responseHeaders": {"entries": [{"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=mock_session_{{ $now.format('x') }}; Path=/; HttpOnly; Secure"}]}}, "id": "jwt-success-response", "name": "JWT Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 240]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"error\": \"Unauthorized\",\n  \"message\": \"Invalid credentials\"\n}", "responseCode": 401}, "id": "jwt-error-response", "name": "JWT Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 360]}, {"parameters": {"httpMethod": "GET", "path": "dokumen-eseal-service/eseal/get-dok-pabean", "responseMode": "responseNode", "options": {}}, "id": "dokumen-pabean-webhook", "name": "Dokumen Pabean Request", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500], "webhookId": "dokumen-pabean"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.query.nomor_aju }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "id": "validate-aju", "name": "Validate AJU", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 500]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Dokumen pabean ditemukan\",\n  \"item\": {\n    \"nomorAju\": \"{{ $json.query.nomor_aju }}\",\n    \"kodeDokumen\": \"BC23\",\n    \"nomorDaftar\": \"{{ $json.query.nomor_aju }}/BC23/2024\",\n    \"tanggalDaftar\": \"{{ $now.format('yyyy-MM-dd') }}\",\n    \"kodeKantor\": \"040300\",\n    \"namaKantor\": \"KPU Bea dan Cukai Tipe Madya Pabean Tanjung Priok\",\n    \"kodeTps\": \"JKTC\",\n    \"namaGudang\": \"PT Terminal Petikemas Jakarta\",\n    \"idPengusaha\": \"{{ $now.format('x').slice(-14) }}\",\n    \"namaPengusaha\": \"PT Mock Eksportir Indonesia\",\n    \"uraian\": \"Barang ekspor berupa tekstil dan garmen untuk testing\",\n    \"kontainer\": [\n      {\n        \"nomorKontainer\": \"MOCK{{ $now.format('x').slice(-6) }}\",\n        \"nomorSegel\": \"SGL{{ $now.format('x').slice(-4) }}\"\n      }\n    ]\n  }\n}"}, "id": "dokumen-success-response", "name": "Dokumen Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 440]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"error\",\n  \"message\": \"Parameter nomor_aju is required\",\n  \"item\": null\n}", "responseCode": 400}, "id": "dokumen-error-response", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 560]}], "connections": {"JWT Token Request": {"main": [[{"node": "Valida<PERSON>", "type": "main", "index": 0}]]}, "Validate Auth": {"main": [[{"node": "JWT Success Response", "type": "main", "index": 0}], [{"node": "JWT Error Response", "type": "main", "index": 0}]]}, "Dokumen Pabean Request": {"main": [[{"node": "Validate AJU", "type": "main", "index": 0}]]}, "Validate AJU": {"main": [[{"node": "Dokumen Success Response", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}